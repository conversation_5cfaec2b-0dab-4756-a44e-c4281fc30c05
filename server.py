#!/usr/bin/env python3
import json
import logging
import uuid
import asyncio
import paho.mqtt.client as mqtt

# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "localhost"
MQTT_PORT = 1884
CLIENT_ID = "b-003"
MQTT_CLIENT_ID = f"{CLIENT_ID}-server"
COMMAND_BASE = f"devices/{CLIENT_ID}/commands"
RESPONSE_BASE = f"devices/{CLIENT_ID}/responses"
TIMEOUT = 5

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# CALLBACKY
# =============================
responses_received = {}

def on_connect(client, userdata, flags, reasonCode, properties=None):
    if reasonCode == mqtt.ReasonCodes.SUCCESS:
        logger.info("Connected to broker")
        client.subscribe(f"{RESPONSE_BASE}/+/+")
        logger.info(f"Subscribed to {RESPONSE_BASE}/+/+")
    else:
        logger.error(f"Failed to connect, reasonCode={reasonCode}")

def on_message(client, userdata, message):
    try:
        payload = json.loads(message.payload.decode())
        logger.info(f"Received response on {message.topic}: {payload}")
        responses_received[payload.get("request_uuid")] = payload
    except Exception as e:
        logger.error(f"Error decoding message: {e}")

# =============================
# UKÁZKOVÉ PŘÍKAZY
# =============================
sample_commands = [
    {"topic": f"{COMMAND_BASE}/electronic/unlock", "payload": {"request_uuid": str(uuid.uuid4()), "section_id": 1}},
    {"topic": f"{COMMAND_BASE}/electronic/section_open", "payload": {"request_uuid": str(uuid.uuid4()), "section_id": 2}},
    {"topic": f"{COMMAND_BASE}/electronic/door_state", "payload": {"request_uuid": str(uuid.uuid4()), "section_id": 3}},
    {"topic": f"{COMMAND_BASE}/system/reboot_device", "payload": {"request_uuid": str(uuid.uuid4())}},
    {"topic": f"{COMMAND_BASE}/sale/edit_reservation", "payload": {"request_uuid": str(uuid.uuid4()), "product_uuid": "fdsa43", "section_id": 3, "status": 1, "price": 99}},
    {"topic": f"{COMMAND_BASE}/sale/reserve_product", "payload": {"request_uuid": str(uuid.uuid4()), "product_uuid": "fdsa43"}},
    {"topic": f"{COMMAND_BASE}/sale/unreserve_product", "payload": {"request_uuid": str(uuid.uuid4()), "product_uuid": "fdsa43", "reservation_pin": 123456}},
    {"topic": f"{COMMAND_BASE}/storage/edit_reservation", "payload": {"request_uuid": str(uuid.uuid4()), "reservation_uuid": "fdsa43", "section_id": 3, "status": 1, "reservation_pin": 123456}},
]

# =============================
# HLAVNÍ
# =============================
async def main():
    client = mqtt.Client(client_id=MQTT_CLIENT_ID, callback_api_version=2)
    client.on_connect = on_connect
    client.on_message = on_message

    # Connect synchronously
    result = client.connect(MQTT_HOST, MQTT_PORT, 60)
    if result != mqtt.MQTT_ERR_SUCCESS:
        logger.error(f"Failed to connect to MQTT broker: {result}")
        return

    # Start the network loop in a separate thread
    client.loop_start()

    # Wait a bit for connection to establish
    await asyncio.sleep(1)

    for cmd in sample_commands:
        request_uuid = cmd["payload"]["request_uuid"]
        logger.info(f"Sending command to {cmd['topic']}: {cmd['payload']}")

        # Publish synchronously
        result = client.publish(cmd["topic"], json.dumps(cmd["payload"]))
        if result.rc != mqtt.MQTT_ERR_SUCCESS:
            logger.error(f"Failed to publish message: {result.rc}")
            continue

        waited = 0
        while waited < TIMEOUT:
            if request_uuid in responses_received:
                logger.info(f"Got response for {request_uuid}: {responses_received[request_uuid]}")
                break
            await asyncio.sleep(0.1)
            waited += 0.1
        else:
            logger.warning(f"No response received for {request_uuid} within {TIMEOUT}s")

    await asyncio.sleep(1)  # počkej na dojití posledních zpráv
    client.loop_stop()
    client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
